'use client'

import React, { useEffect, useRef } from 'react'

interface PerformanceMetrics {
  renderTime: number
  componentName: string
  timestamp: number
}

export function usePerformanceMonitor(componentName: string) {
  const startTimeRef = useRef<number>(0)
  const metricsRef = useRef<PerformanceMetrics[]>([])

  useEffect(() => {
    startTimeRef.current = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTimeRef.current
      
      const metric: PerformanceMetrics = {
        renderTime,
        componentName,
        timestamp: Date.now()
      }
      
      metricsRef.current.push(metric)
      
      // Log slow renders (> 16ms for 60fps)
      if (renderTime > 16) {
        console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`)
      }
      
      // Keep only last 10 metrics to prevent memory leaks
      if (metricsRef.current.length > 10) {
        metricsRef.current = metricsRef.current.slice(-10)
      }
    }
  })

  const getMetrics = () => metricsRef.current
  const getAverageRenderTime = () => {
    const metrics = metricsRef.current
    if (metrics.length === 0) return 0
    return metrics.reduce((sum, metric) => sum + metric.renderTime, 0) / metrics.length
  }

  return { getMetrics, getAverageRenderTime }
}

export function useLoadingState(initialLoading = false) {
  const [loading, setLoading] = React.useState(initialLoading)
  const [error, setError] = useState<string | null>(null)
  
  const startLoading = () => {
    setLoading(true)
    setError(null)
  }
  
  const stopLoading = () => setLoading(false)
  
  const setLoadingError = (errorMessage: string) => {
    setLoading(false)
    setError(errorMessage)
  }
  
  return {
    loading,
    error,
    startLoading,
    stopLoading,
    setLoadingError
  }
}
