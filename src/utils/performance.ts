// Performance utilities for monitoring and optimization

export function measurePageLoad() {
  if (typeof window === 'undefined') return null

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  
  return {
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
    firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
    firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
    totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
  }
}

export function logPerformanceMetrics() {
  if (typeof window === 'undefined') return

  setTimeout(() => {
    const metrics = measurePageLoad()
    if (metrics) {
      console.group('🚀 Performance Metrics')
      console.log('DOM Content Loaded:', `${metrics.domContentLoaded.toFixed(2)}ms`)
      console.log('Load Complete:', `${metrics.loadComplete.toFixed(2)}ms`)
      console.log('First Paint:', `${metrics.firstPaint.toFixed(2)}ms`)
      console.log('First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`)
      console.log('Total Load Time:', `${metrics.totalLoadTime.toFixed(2)}ms`)
      console.groupEnd()

      // Warn about slow metrics
      if (metrics.totalLoadTime > 3000) {
        console.warn('⚠️ Slow page load detected:', `${metrics.totalLoadTime.toFixed(2)}ms`)
      }
      if (metrics.firstContentfulPaint > 1500) {
        console.warn('⚠️ Slow First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`)
      }
    }
  }, 1000)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
